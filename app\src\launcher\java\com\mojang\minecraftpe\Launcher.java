package com.mojang.minecraftpe;

import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Iterator;

/**
 * Launcher class that extends MainActivity to provide custom asset and storage path handling.
 * This class allows loading Minecraft with custom APK sources and redirected storage paths.
 * 
 * This source code was reverse-engineered from the original Minecraft APK for compatibility
 * purposes and is provided here to comply with GPL licensing requirements.
 */
public class Launcher extends MainActivity {
    private String mcPath;

    @Override
    public void onCreate(Bundle bundle) throws IllegalAccessException, NoSuchMethodException, 
            SecurityException, IllegalArgumentException, InvocationTargetException {
        try {
            this.mcPath = getIntent().getStringExtra("MC_PATH");
            
            // Use reflection to add custom asset paths
            Method declaredMethod = getAssets().getClass().getDeclaredMethod("addAssetPath", String.class);
            declaredMethod.invoke(getAssets(), getIntent().getStringExtra("MC_SRC"));
            
            // Handle split APKs if provided
            ArrayList<String> stringArrayListExtra = getIntent().getStringArrayListExtra("MC_SPLIT_SRC");
            if (stringArrayListExtra != null) {
                Iterator<String> it = stringArrayListExtra.iterator();
                while (it.hasNext()) {
                    declaredMethod.invoke(getAssets(), it.next());
                }
            }
            
            super.onCreate(bundle);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    static {
        System.loadLibrary("c++_shared");
        System.loadLibrary("fmod");
        System.loadLibrary("minecraftpe");
        System.loadLibrary("preloader");
    }

    /**
     * Returns the external storage path, either custom MC_PATH or default external files dir.
     */
    public String getExternalStoragePath() {
        return this.mcPath.isEmpty() ? getExternalFilesDir(null).getAbsolutePath() : this.mcPath;
    }

    /**
     * Returns the legacy external storage path with write permission check.
     */
    public String getLegacyExternalStoragePath(String str) throws IOException {
        boolean z;
        if (this.mcPath.isEmpty()) {
            File externalStorageDirectory = Environment.getExternalStorageDirectory();
            try {
                new FileOutputStream(new File(new File(externalStorageDirectory, str), "test")).close();
                z = true;
            } catch (Exception e) {
                z = false;
            }
            return z ? externalStorageDirectory.getAbsolutePath() : "";
        }
        return "";
    }

    /**
     * Returns the internal storage path, either custom MC_PATH or default data/files dir.
     */
    public String getInternalStoragePath() {
        if (this.mcPath.isEmpty()) {
            if (Build.VERSION.SDK_INT >= 24) {
                return getDataDir().getAbsolutePath();
            }
            return getFilesDir().getParent();
        }
        return this.mcPath;
    }
}
